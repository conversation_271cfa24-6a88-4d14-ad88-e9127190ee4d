"""
AI Service Integration for Test Case Generator
Supports OpenAI ChatGPT and Google Gemini APIs
"""

import os
import json
import re
from typing import List, Dict, Any, Optional
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False


class AIService:
    """AI service for generating test cases using OpenAI or Gemini"""
    
    def __init__(self):
        self.openai_api_key = os.getenv('OPENAI_API_KEY')
        self.gemini_api_key = os.getenv('GEMINI_API_KEY')
        self.openai_model = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')
        self.gemini_model = os.getenv('GEMINI_MODEL', 'gemini-pro')
        self.max_tokens = int(os.getenv('API_MAX_TOKENS', '4000'))
        self.timeout = int(os.getenv('API_TIMEOUT', '30'))
        
        # Initialize clients
        self._init_openai()
        self._init_gemini()
    
    def _init_openai(self):
        """Initialize OpenAI client"""
        if OPENAI_AVAILABLE and self.openai_api_key:
            openai.api_key = self.openai_api_key
            self.openai_client = openai.OpenAI(api_key=self.openai_api_key)
        else:
            self.openai_client = None
    
    def _init_gemini(self):
        """Initialize Gemini client"""
        if GEMINI_AVAILABLE and self.gemini_api_key:
            genai.configure(api_key=self.gemini_api_key)
            self.gemini_client = genai.GenerativeModel(self.gemini_model)
        else:
            self.gemini_client = None
    
    def is_openai_available(self) -> bool:
        """Check if OpenAI is available and configured"""
        return OPENAI_AVAILABLE and self.openai_client is not None
    
    def is_gemini_available(self) -> bool:
        """Check if Gemini is available and configured"""
        return GEMINI_AVAILABLE and self.gemini_client is not None
    
    def get_available_models(self) -> List[str]:
        """Get list of available AI models"""
        models = []
        if self.is_openai_available():
            models.append("ChatGPT")
        if self.is_gemini_available():
            models.append("Gemini")
        return models
    
    def create_professional_prompt(self, content: str) -> str:
        """Create a professional prompt for test case generation"""
        prompt = f"""
Bạn là một chuyên gia kiểm thử phần mềm chuyên nghiệp với nhiều năm kinh nghiệm trong việc tạo test case toàn diện. Nhiệm vụ của bạn là phân tích tài liệu yêu cầu chức năng được cung cấp và tạo ra các test case chi tiết, chuyên nghiệp bằng tiếng Việt.

YÊU CẦU PHÂN TÍCH:
{content}

HƯỚNG DẪN:
1. Tạo test case toàn diện bao phủ TẤT CẢ các khía cạnh của chức năng
2. Bao gồm kiểm thử UI, kiểm thử chức năng, kiểm thử validation, edge cases và các tình huống lỗi
3. Cấu trúc test case với phân loại rõ ràng
4. Sử dụng thuật ngữ tiếng Việt chuyên nghiệp
5. Tuân thủ chính xác định dạng được chỉ định bên dưới

ĐỊNH DẠNG ĐẦU RA:
Tạo test case theo định dạng CSV với các cột: ID, Mục đích kiểm thử, Các bước thực hiện, Kết quả mong muốn, Độ ưu tiên

VÍ DỤ ĐẦU VÀO:
Chức năng cần kiểm thử (URD)
1.1. Mục đích: Tài liệu này mô tả yêu cầu của người dùng đối với chức năng Quản lý chức danh, bao gồm: Tạo mới, Sửa, Xóa và Tìm kiếm chức danh trong hệ thống.
2.1. Tạo mới chức danh
- Tên chức danh (bắt buộc, tối đa 64 ký tự, không trùng lặp)
- Mô tả (không bắt buộc, tối đa 256 ký tự)
- Nút Lưu: Thêm chức danh vào hệ thống
- Nút Hủy: Đóng popup không lưu

VÍ DỤ ĐẦU RA:
ID,Mục đích kiểm thử,Các bước thực hiện,Kết quả mong muốn,Độ ưu tiên
,Kiểm tra UI,,,
TC_UI_001,Kiểm tra hiển thị popup Tạo mới chức danh,1. Mở phân hệ Quản trị hệ thống \\n 2. Nhấn nút "Tạo mới chức danh",Popup "Tạo mới chức danh" hiển thị với các trường Tên chức danh, Mô tả và nút Lưu, Hủy,High
TC_UI_002,Kiểm tra hiển thị các trường bắt buộc,1. Mở popup Tạo mới chức danh \\n 2. Kiểm tra hiển thị dấu * cho trường bắt buộc,Trường "Tên chức danh" hiển thị dấu * hoặc nhãn "bắt buộc",High
,Kiểm tra validate trường Tạo mới chức danh,,,
TC_VAL_001,Kiểm tra tạo mới với thông tin hợp lệ,1. Mở phân hệ Quản trị hệ thống \\n 2. Nhấn nút Tạo mới \\n 3. Nhập Tên chức danh (ví dụ: "Quản lý") và Mô tả \\n 4. Nhấn Lưu,Chức danh được thêm thành công, hiển thị thông báo thành công,High
TC_VAL_002,Kiểm tra tạo mới khi bỏ trống Tên chức danh,1. Mở phân hệ Quản trị hệ thống \\n 2. Nhấn nút Tạo mới \\n 3. Để trống Tên chức danh \\n 4. Nhấn Lưu,Hiển thị lỗi "Vui lòng nhập Tên chức danh",High
TC_VAL_003,Kiểm tra tạo mới với Tên chức danh trùng lặp,1. Mở phân hệ Quản trị hệ thống \\n 2. Nhấn nút Tạo mới \\n 3. Nhập Tên chức danh đã tồn tại \\n 4. Nhấn Lưu,Hiển thị lỗi "Tên chức danh đã tồn tại",High
TC_VAL_004,Kiểm tra tạo mới với Tên chức danh vượt quá 64 ký tự,1. Mở phân hệ Quản trị hệ thống \\n 2. Nhấn nút Tạo mới \\n 3. Nhập Tên chức danh vượt quá 64 ký tự \\n 4. Nhấn Lưu,Hiển thị lỗi "Tên chức danh không được vượt quá 64 ký tự",Normal

HƯỚNG DẪN QUAN TRỌNG:
- Tạo test case cho TẤT CẢ các chức năng được đề cập trong yêu cầu
- Bao gồm kiểm thử biên (giá trị tối thiểu/tối đa)
- Bao phủ các tình huống tích cực và tiêu cực
- Bao gồm các cân nhắc về bảo mật và hiệu năng khi phù hợp
- Sử dụng thuật ngữ tiếng Việt phù hợp
- Đảm bảo các bước test rõ ràng và có thể thực hiện được
- Đặt mức độ ưu tiên phù hợp (High, Normal, Low)
- Nhóm các test case liên quan dưới các tiêu đề danh mục

Bây giờ hãy tạo test case toàn diện cho yêu cầu được cung cấp:
"""
        return prompt
    
    def generate_with_openai(self, content: str) -> str:
        """Generate test cases using OpenAI ChatGPT"""
        if not self.is_openai_available():
            raise ValueError("OpenAI is not available or not configured")
        
        prompt = self.create_professional_prompt(content)
        
        try:
            response = self.openai_client.chat.completions.create(
                model=self.openai_model,
                messages=[
                    {"role": "system", "content": "Bạn là một chuyên gia kiểm thử phần mềm chuyên nghiệp tạo test case bằng tiếng Việt."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=self.max_tokens,
                temperature=0.7,
                timeout=self.timeout
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            raise Exception(f"OpenAI API error: {str(e)}")
    
    def generate_with_gemini(self, content: str) -> str:
        """Generate test cases using Google Gemini"""
        if not self.is_gemini_available():
            raise ValueError("Gemini is not available or not configured")
        
        prompt = self.create_professional_prompt(content)
        
        try:
            response = self.gemini_client.generate_content(
                prompt,
                generation_config=genai.types.GenerationConfig(
                    max_output_tokens=self.max_tokens,
                    temperature=0.7,
                )
            )
            
            return response.text.strip()
            
        except Exception as e:
            raise Exception(f"Gemini API error: {str(e)}")
    
    def generate_test_cases(self, content: str, model: str = "ChatGPT") -> str:
        """
        Generate test cases using the specified AI model
        
        Args:
            content: Functional requirements content
            model: AI model to use ("ChatGPT" or "Gemini")
            
        Returns:
            Generated test cases as CSV string
        """
        if model == "ChatGPT":
            return self.generate_with_openai(content)
        elif model == "Gemini":
            return self.generate_with_gemini(content)
        else:
            raise ValueError(f"Unsupported model: {model}")
    
    def parse_csv_response(self, csv_content: str) -> List[Dict[str, str]]:
        """Parse CSV response from AI into list of dictionaries"""
        lines = csv_content.strip().split('\n')
        if not lines:
            return []
        
        # Find the header line (should contain the column names)
        header_line = None
        for i, line in enumerate(lines):
            if 'ID,Mục đích kiểm thử' in line or 'ID,' in line:
                header_line = i
                break
        
        if header_line is None:
            # If no proper header found, assume first line is header
            header_line = 0
        
        headers = ['ID', 'Mục đích kiểm thử', 'Các bước thực hiện', 'Kết quả mong muốn', 'Độ ưu tiên']
        test_cases = []
        
        for line in lines[header_line + 1:]:
            if not line.strip():
                continue
            
            # Simple CSV parsing (handles basic cases)
            parts = []
            current_part = ""
            in_quotes = False
            
            for char in line:
                if char == '"':
                    in_quotes = not in_quotes
                elif char == ',' and not in_quotes:
                    parts.append(current_part.strip())
                    current_part = ""
                else:
                    current_part += char
            
            parts.append(current_part.strip())
            
            # Ensure we have enough parts
            while len(parts) < len(headers):
                parts.append("")
            
            # Create test case dictionary
            test_case = {}
            for i, header in enumerate(headers):
                value = parts[i] if i < len(parts) else ""
                # Clean up the value
                value = value.strip('"').strip()
                test_case[header] = value
            
            test_cases.append(test_case)
        
        return test_cases
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get status of AI services"""
        return {
            'openai': {
                'available': self.is_openai_available(),
                'model': self.openai_model if self.is_openai_available() else None,
                'configured': bool(self.openai_api_key)
            },
            'gemini': {
                'available': self.is_gemini_available(),
                'model': self.gemini_model if self.is_gemini_available() else None,
                'configured': bool(self.gemini_api_key)
            }
        }
