"""
Test Case Generator Chatbot Application
A Gradio-based application for generating comprehensive test cases
"""

import gradio as gr
import os
import tempfile
from typing import List, Dict, Any, Optional, Tu<PERSON>

from project_manager import ProjectManager
from file_parser import FileParser
from test_case_generator import TestCaseGenerator
from output_handler import OutputHandler


class TestCaseGeneratorApp:
    """Main application class for the Test Case Generator"""
    
    def __init__(self):
        self.project_manager = ProjectManager()
        self.file_parser = FileParser()
        self.test_generator = TestCaseGenerator()
        self.output_handler = OutputHandler()
        
        # Application state
        self.current_project_id = None
        self.uploaded_files = []
        self.generated_test_cases = []
        self.output_files = {'txt': None, 'excel': None}
    
    def create_project(self, name: str, description: str) -> Tuple[str, str]:
        """Create a new project"""
        try:
            if not name.strip():
                return "❌ Project name cannot be empty!", ""

            project_id = self.project_manager.create_project(name.strip(), description.strip())
            self.current_project_id = project_id

            # Update project dropdown
            project_names = self.project_manager.get_project_names()

            return f"✅ Successfully created project '{name}'!", gr.Dropdown(choices=project_names, value=name)

        except ValueError as e:
            return f"❌ Error: {str(e)}", gr.Dropdown()
        except Exception as e:
            return f"❌ Unexpected error: {str(e)}", gr.Dropdown()
    
    def select_project(self, project_name: str) -> str:
        """Select an existing project"""
        if not project_name:
            self.current_project_id = None
            return "⚠️ No project selected."

        project = self.project_manager.get_project_by_name(project_name)
        if project:
            self.current_project_id = project['id']
            files_count = len(project.get('files', []))
            outputs_count = len(project.get('generated_outputs', []))
            return f"✅ Selected project '{project_name}'. Contains {files_count} uploaded file(s) and {outputs_count} generated output(s)."
        else:
            self.current_project_id = None
            return f"❌ Project '{project_name}' not found."
    
    def upload_files(self, files) -> str:
        """Handle file uploads with project-specific storage"""
        if not self.current_project_id:
            return "❌ Please select or create a project before uploading files."

        if not files:
            return "❌ No files selected for upload."

        uploaded_count = 0
        error_messages = []

        # Get project-specific inputs directory
        project_inputs_path = self.project_manager.get_project_inputs_path(self.current_project_id)
        if not project_inputs_path:
            return "❌ Unable to determine project storage location."

        for file in files:
            try:
                if not self.file_parser.is_supported_file(file.name):
                    error_messages.append(f"File '{file.name}' format not supported.")
                    continue

                # Extract clean original filename
                original_filename = os.path.basename(file.name)

                # Save file to project-specific inputs directory
                saved_path, file_type = self.file_parser.save_uploaded_file(
                    file,
                    original_filename,
                    project_inputs_path
                )

                # Add to project with clean original filename
                self.project_manager.add_file_to_project(
                    self.current_project_id,
                    saved_path,
                    original_filename,
                    file_type
                )

                uploaded_count += 1

            except Exception as e:
                error_messages.append(f"Upload error for '{os.path.basename(file.name)}': {str(e)}")

        result_message = f"✅ Successfully uploaded {uploaded_count} file(s) to project storage."
        if error_messages:
            result_message += f"\n⚠️ Issues encountered:\n" + "\n".join(error_messages)

        return result_message
    
    def generate_test_cases(self, ai_model: str) -> Tuple[str, str, str, str, str]:
        """Generate test cases from uploaded files"""
        if not self.current_project_id:
            return "❌ Please select a project first.", None, None, None, None

        project = self.project_manager.get_project(self.current_project_id)
        if not project:
            return "❌ Project not found.", None, None, None, None

        files = project.get('files', [])
        if not files:
            return "❌ No files uploaded. Please upload requirement documents first.", None, None, None, None
        
        try:
            # Parse all uploaded files and combine content
            combined_content = []
            
            for file_info in files:
                file_path = file_info['path']
                file_type = file_info['file_type']
                
                if os.path.exists(file_path):
                    content = self.file_parser.parse_file(file_path, file_type)
                    combined_content.append(f"=== File: {file_info['original_name']} ===\n{content}\n")
            
            if not combined_content:
                return "❌ Unable to read content from uploaded files.", None, None, None, None

            # Combine all content
            full_content = "\n".join(combined_content)

            # Generate test cases using selected AI model
            selected_model = None if ai_model == "Mock (Built-in Generator)" else ai_model
            self.generated_test_cases = self.test_generator.generate_test_cases(full_content, selected_model)

            # Mark input files as processed
            for file_info in files:
                if file_info.get('id'):
                    self.project_manager.update_file_processing_status(
                        self.current_project_id,
                        file_info['id'],
                        'processed'
                    )
            
            if not self.generated_test_cases:
                return "❌ Unable to generate test cases from file content.", None, None, None, None

            # Generate output files with model information
            txt_path, excel_path = self.output_handler.generate_output_files(
                self.generated_test_cases,
                project['name'],
                selected_model if selected_model else "Mock"
            )

            # Save output file paths to project
            self.project_manager.add_generated_output(
                self.current_project_id,
                txt_path,
                excel_path,
                selected_model if selected_model else "Mock"
            )

            # Store for download
            self.output_files = {'txt': txt_path, 'excel': excel_path}

            # Format for display
            display_content = self.output_handler.format_test_cases_for_display(self.generated_test_cases)

            test_case_count = len([tc for tc in self.generated_test_cases if tc.get('ID', '').strip()])

            model_used = selected_model if selected_model else "Built-in Generator"

            return (
                f"✅ Successfully generated {test_case_count} professional test cases using {model_used}!",
                display_content,
                f"📁 Generated Files:\n• {os.path.basename(txt_path)} (Text Format)\n• {os.path.basename(excel_path)} (Excel Format)",
                txt_path,
                excel_path
            )

        except Exception as e:
            return f"❌ Error generating test cases: {str(e)}", None, None, None, None
    
    def download_txt_file(self):
        """Return path to TXT file for download"""
        if self.output_files.get('txt') and os.path.exists(self.output_files['txt']):
            return self.output_files['txt']
        return None
    
    def download_excel_file(self):
        """Return path to Excel file for download"""
        if self.output_files.get('excel') and os.path.exists(self.output_files['excel']):
            return self.output_files['excel']
        return None
    
    def get_project_choices(self):
        """Get current project choices for dropdown"""
        return self.project_manager.get_project_names()

    def get_file_management_data(self, project_name: str) -> str:
        """Get enhanced file management data with professional gallery-style display"""
        if not project_name:
            return "📂 **Professional File Management**\n\n⚠️ No project selected. Please choose a project from the dropdown above to view and manage files."

        project = self.project_manager.get_project_by_name(project_name)
        if not project:
            return f"📂 **Professional File Management**\n\n❌ Project '{project_name}' not found."

        project_id = project['id']
        files = self.project_manager.get_project_files(project_id)
        categorized = self.project_manager.get_project_files_by_category(project_id)
        stats = self.project_manager.get_file_stats(project_id)

        if not files:
            return f"""📂 **Professional File Management - {project_name}**

🎯 **Project Overview**
• Status: Empty project - ready for file uploads
• Storage Location: `projects/{project_name}/inputs/`
• Supported Formats: .txt, .docx, .xlsx, .xls

💡 **Quick Start:**
1. Upload requirement documents using the file upload component above
2. Files will be automatically organized in project-specific folders
3. Generate test cases from uploaded requirements
4. Download results in multiple formats"""

        # Build enhanced file management display
        output = [f"📂 **Professional File Management - {project_name}**"]
        output.append("")
        output.append("🎯 **Project Statistics**")
        output.append(f"• **Total Files:** {stats['total_files']} files")
        output.append(f"• **Storage Used:** {self.project_manager.format_file_size(stats['total_size'])}")
        output.append(f"• **Input Files:** {stats['input_files']} documents")
        output.append(f"• **Generated Outputs:** {stats['output_files']} files")
        output.append("")

        # Enhanced Input Files Section
        input_count = len(categorized['input'])
        output.append("📤 **Input Documents & Requirements**")
        output.append(f"*{input_count} requirement document(s) uploaded*")
        output.append("")

        if categorized['input']:
            for i, file_info in enumerate(categorized['input'], 1):
                status_icon = "✅" if file_info.get('processing_status') == 'processed' else "⏳"
                file_type_icon = self.get_file_type_icon(file_info.get('file_type', ''))
                file_type_desc = self.get_file_type_description(file_info.get('file_type', ''))
                size_str = self.project_manager.format_file_size(file_info.get('file_size', 0))
                upload_time = self.format_datetime(file_info.get('uploaded_at', ''))
                file_id = file_info.get('id', '')
                original_name = file_info.get('original_name', 'Unknown')
                processing_status = file_info.get('processing_status', 'pending').title()

                # Professional file card display
                output.append(f"**{i}. {file_type_icon} {original_name}**")
                output.append(f"   • **Type:** {file_type_desc}")
                output.append(f"   • **Size:** {size_str}")
                output.append(f"   • **Uploaded:** {upload_time}")
                output.append(f"   • **Status:** {status_icon} {processing_status}")
                output.append(f"   • **Actions:** 🗑️ [Delete File](delete:{file_id}:{original_name})")
                output.append("")
        else:
            output.append("📭 **No input files uploaded yet**")
            output.append("*Upload requirement documents using the file upload component above*")
            output.append("")

        # Enhanced Output Files Section
        output_count = len(categorized['output'])
        output.append("📋 **Generated Test Cases & Outputs**")
        output.append(f"*{output_count} generated file(s) available for download*")
        output.append("")

        if categorized['output']:
            # Group output files by generation session
            output_groups = {}
            for file_info in categorized['output']:
                model_used = file_info.get('model_used', 'Unknown')
                generated_time = self.format_datetime(file_info.get('uploaded_at', ''))
                group_key = f"{model_used}_{generated_time}"

                if group_key not in output_groups:
                    output_groups[group_key] = {
                        'model': model_used,
                        'time': generated_time,
                        'files': []
                    }
                output_groups[group_key]['files'].append(file_info)

            # Display grouped outputs
            for group_idx, (group_key, group_data) in enumerate(output_groups.items(), 1):
                output.append(f"**Generation Session {group_idx} - {group_data['model']} Model**")
                output.append(f"   • **Generated:** {group_data['time']}")
                output.append(f"   • **Files:** {len(group_data['files'])} output file(s)")

                for file_info in group_data['files']:
                    file_type_icon = self.get_file_type_icon(file_info.get('file_type', ''))
                    file_type_desc = self.get_file_type_description(file_info.get('file_type', ''))
                    size_str = self.project_manager.format_file_size(file_info.get('file_size', 0))
                    file_id = file_info.get('id', '')
                    friendly_name = self.get_friendly_output_name(file_info, project_name)

                    output.append(f"     ├─ {file_type_icon} **{friendly_name}**")
                    output.append(f"        • Type: {file_type_desc} • Size: {size_str}")
                    output.append(f"        • Actions: 🗑️ [Delete File](delete:{file_id}:{friendly_name})")

                output.append("")
        else:
            output.append("📭 **No test cases generated yet**")
            output.append("*Generate test cases from uploaded requirements to see outputs here*")
            output.append("")

        # Professional footer with tips and shortcuts
        output.append("---")
        output.append("💡 **File Management Tips**")
        output.append("• **Status Icons:** ⏳ Pending Processing, ✅ Processed & Ready")
        output.append("• **Quick Actions:** Click 🗑️ to delete files (with confirmation)")
        output.append("• **File Organization:** Files are stored in `projects/{}/inputs/` and `projects/{}/outputs/`".format(project_name, project_name))
        output.append("• **Supported Formats:** Text (.txt), Word (.docx), Excel (.xlsx, .xls)")
        output.append("")
        output.append("🔄 **Refresh this view after uploading new files or generating test cases**")

        return "\n".join(output)

    def get_file_type_icon(self, file_type: str) -> str:
        """Get professional icon for file type"""
        icons = {
            '.txt': '📄',      # Text document
            '.docx': '📝',     # Word document
            '.xlsx': '📊',     # Excel spreadsheet
            '.xls': '📊',      # Legacy Excel
            '.pdf': '📕',      # PDF document
            '.csv': '📋',      # CSV file
            '.json': '🔧',     # JSON file
            '.xml': '🏷️',      # XML file
            '.md': '📖',       # Markdown
            '.zip': '📦',      # Archive
            '.rar': '📦',      # Archive
            '.7z': '📦'        # Archive
        }
        return icons.get(file_type.lower(), '📄')

    def get_file_type_description(self, file_type: str) -> str:
        """Get human-readable description for file type"""
        descriptions = {
            '.txt': 'Text Document',
            '.docx': 'Word Document',
            '.xlsx': 'Excel Spreadsheet',
            '.xls': 'Excel Spreadsheet (Legacy)',
            '.pdf': 'PDF Document',
            '.csv': 'CSV Data File',
            '.json': 'JSON Data File',
            '.xml': 'XML Document',
            '.md': 'Markdown Document',
            '.zip': 'ZIP Archive',
            '.rar': 'RAR Archive',
            '.7z': '7-Zip Archive'
        }
        return descriptions.get(file_type.lower(), 'Unknown File Type')

    def format_datetime(self, datetime_str: str) -> str:
        """Format datetime string for display"""
        if not datetime_str:
            return "Unknown"
        try:
            # Parse ISO format datetime
            from datetime import datetime
            dt = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            return dt.strftime("%Y-%m-%d %H:%M")
        except:
            return datetime_str.split('T')[0] if 'T' in datetime_str else datetime_str

    def get_friendly_output_name(self, file_info: dict, project_name: str) -> str:
        """Generate user-friendly name for output files"""
        file_type = file_info.get('file_type', '').lower()
        model_used = file_info.get('model_used', 'Mock')

        if file_type == '.txt':
            return f"{project_name} - Test Cases (Text Format) - {model_used}"
        elif file_type in ['.xlsx', '.xls']:
            return f"{project_name} - Test Cases (Excel Format) - {model_used}"
        else:
            return file_info.get('original_name', 'Unknown File')

    def parse_delete_action(self, file_listing_content: str) -> tuple:
        """Parse delete action from file listing content"""
        # Look for delete links in the format: [Delete File](delete:file_id:filename)
        import re
        delete_pattern = r'\[Delete File\]\(delete:([^:]+):([^)]+)\)'
        matches = re.findall(delete_pattern, file_listing_content)

        if matches:
            # Return the last clicked delete action
            file_id, filename = matches[-1]
            return file_id, filename

        return None, None

    def get_file_details_for_confirmation(self, project_name: str, file_id: str) -> str:
        """Get enhanced file details for deletion confirmation"""
        if not project_name or not file_id:
            return "❌ **Invalid Selection**\n\nUnable to identify the file for deletion. Please try again."

        project = self.project_manager.get_project_by_name(project_name)
        if not project:
            return f"❌ **Project Not Found**\n\nProject '{project_name}' could not be located."

        files = self.project_manager.get_project_files(project['id'])
        for file_info in files:
            if file_info.get('id') == file_id:
                size_str = self.project_manager.format_file_size(file_info.get('file_size', 0))
                upload_time = self.format_datetime(file_info.get('uploaded_at', ''))
                file_type = file_info.get('file_type', 'Unknown')
                file_type_desc = self.get_file_type_description(file_type)
                file_type_icon = self.get_file_type_icon(file_type)
                category = file_info.get('category', 'Unknown')
                processing_status = file_info.get('processing_status', 'unknown')

                if category == 'output':
                    friendly_name = self.get_friendly_output_name(file_info, project_name)
                    model_used = file_info.get('model_used', 'Unknown')
                else:
                    friendly_name = file_info.get('original_name', 'Unknown')
                    model_used = None

                # Enhanced confirmation dialog
                confirmation_text = f"""🗑️ **Confirm File Deletion**

{file_type_icon} **File Information:**
• **Name:** {friendly_name}
• **Type:** {file_type_desc} ({file_type.upper()})
• **Category:** {category.title()} File
• **Size:** {size_str}
• **Date:** {upload_time}"""

                if category == 'input':
                    confirmation_text += f"""
• **Status:** {processing_status.title()}"""
                elif category == 'output' and model_used:
                    confirmation_text += f"""
• **Generated by:** {model_used} AI Model"""

                confirmation_text += f"""

⚠️ **IMPORTANT WARNING**
This action will **permanently delete** the file from:
• Project storage location
• File system (`projects/{project_name}/{'inputs' if category == 'input' else 'outputs'}/`)
• Project database records

❌ **This action cannot be undone!**

🤔 **Are you absolutely sure you want to delete this file?**"""

                return confirmation_text

        return "❌ **File Not Found**\n\nThe selected file could not be located in the project. It may have already been deleted."

    def delete_file(self, project_name: str, file_id: str) -> str:
        """Delete a file from project with enhanced feedback"""
        if not project_name or not file_id:
            return "❌ **Invalid Request**\n\nMissing required parameters for file deletion."

        project = self.project_manager.get_project_by_name(project_name)
        if not project:
            return f"❌ **Project Not Found**\n\nProject '{project_name}' could not be located."

        # Get file details before deletion for comprehensive feedback
        files = self.project_manager.get_project_files(project['id'])
        deleted_file_info = None
        for file_info in files:
            if file_info.get('id') == file_id:
                deleted_file_info = file_info
                break

        if not deleted_file_info:
            return "❌ **File Not Found**\n\nThe selected file could not be located. It may have already been deleted."

        # Extract file details for feedback
        if deleted_file_info.get('category') == 'output':
            deleted_file_name = self.get_friendly_output_name(deleted_file_info, project_name)
        else:
            deleted_file_name = deleted_file_info.get('original_name', 'Unknown file')

        file_type_icon = self.get_file_type_icon(deleted_file_info.get('file_type', ''))
        file_size = self.project_manager.format_file_size(deleted_file_info.get('file_size', 0))
        category = deleted_file_info.get('category', 'unknown')

        try:
            success = self.project_manager.delete_file_from_project(project['id'], file_id)
            if success:
                return f"""✅ **File Successfully Deleted**

{file_type_icon} **Deleted File:** {deleted_file_name}
📏 **Size Freed:** {file_size}
📁 **Category:** {category.title()} file
🗂️ **Project:** {project_name}

The file has been permanently removed from:
• Project storage directory
• File system
• Database records

💡 **Tip:** Use the refresh button to update the file listing."""
            else:
                return f"""❌ **Deletion Failed**

{file_type_icon} **File:** {deleted_file_name}
🗂️ **Project:** {project_name}

The file could not be deleted. Possible reasons:
• File is currently in use by another process
• Insufficient permissions
• File has already been deleted

Please try again or contact support if the issue persists."""
        except Exception as e:
            return f"""❌ **Deletion Error**

{file_type_icon} **File:** {deleted_file_name}
🗂️ **Project:** {project_name}
⚠️ **Error:** {str(e)}

An unexpected error occurred during deletion. Please try again or contact support."""
    
    def create_interface(self):
        """Create the Gradio interface"""
        with gr.Blocks(
            title="Professional Test Case Generator - Enhanced File Management",
            theme=gr.themes.Soft(),
            css="""
            /* Enhanced Professional Design System */
            .main-header {
                text-align: center;
                color: #1e3a8a;
                margin-bottom: 2rem;
                padding: 2rem;
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 16px;
                border: 1px solid #cbd5e1;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            }
            .section-header {
                color: #1e40af;
                border-bottom: 3px solid #3b82f6;
                padding-bottom: 0.75rem;
                margin: 2rem 0 1.5rem 0;
                font-weight: 600;
                font-size: 1.25rem;
                position: relative;
            }
            .section-header::before {
                content: '';
                position: absolute;
                left: 0;
                bottom: -3px;
                width: 60px;
                height: 3px;
                background: linear-gradient(90deg, #3b82f6, #1d4ed8);
                border-radius: 2px;
            }
            .professional-card {
                background: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 1.5rem;
                margin: 1rem 0;
                box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.05);
                transition: all 0.2s ease-in-out;
            }
            .professional-card:hover {
                box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.1);
                border-color: #cbd5e1;
            }
            .file-management-card {
                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 1.5rem;
                margin: 1rem 0;
                box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
            }
            .status-success {
                background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
                border: 1px solid #86efac;
                color: #166534;
                padding: 1rem;
                border-radius: 8px;
                margin: 0.5rem 0;
                font-weight: 500;
            }
            .status-error {
                background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
                border: 1px solid #fca5a5;
                color: #dc2626;
                padding: 1rem;
                border-radius: 8px;
                margin: 0.5rem 0;
                font-weight: 500;
            }
            .status-info {
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                border: 1px solid #93c5fd;
                color: #1d4ed8;
                padding: 1rem;
                border-radius: 8px;
                margin: 0.5rem 0;
                font-weight: 500;
            }
            .ai-status-card {
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                border: 1px solid #cbd5e1;
                border-radius: 12px;
                padding: 1.5rem;
                margin: 1rem 0;
                box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            }
            .divider {
                height: 2px;
                background: linear-gradient(to right, transparent, #cbd5e1, transparent);
                margin: 2.5rem 0;
                border-radius: 1px;
            }
            /* Enhanced Button Styling */
            .primary-btn {
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                border: none;
                color: white;
                font-weight: 600;
                border-radius: 8px;
                transition: all 0.2s ease-in-out;
                box-shadow: 0 2px 4px 0 rgba(59, 130, 246, 0.3);
            }
            .primary-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px 0 rgba(59, 130, 246, 0.4);
            }
            .secondary-btn {
                background: linear-gradient(135deg, #64748b 0%, #475569 100%);
                border: none;
                color: white;
                font-weight: 500;
                border-radius: 8px;
                transition: all 0.2s ease-in-out;
                box-shadow: 0 2px 4px 0 rgba(100, 116, 139, 0.3);
            }
            .secondary-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px 0 rgba(100, 116, 139, 0.4);
            }
            .danger-btn {
                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
                border: none;
                color: white;
                font-weight: 600;
                border-radius: 8px;
                transition: all 0.2s ease-in-out;
                box-shadow: 0 2px 4px 0 rgba(239, 68, 68, 0.3);
            }
            .danger-btn:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px 0 rgba(239, 68, 68, 0.4);
            }
            /* Responsive Design */
            @media (max-width: 768px) {
                .main-header {
                    padding: 1rem;
                    margin-bottom: 1rem;
                }
                .professional-card {
                    padding: 1rem;
                    margin: 0.5rem 0;
                }
                .section-header {
                    font-size: 1.1rem;
                    margin: 1.5rem 0 1rem 0;
                }
            }
            /* File Upload Enhancement */
            .file-upload-area {
                border: 2px dashed #cbd5e1;
                border-radius: 12px;
                padding: 2rem;
                text-align: center;
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                transition: all 0.2s ease-in-out;
            }
            .file-upload-area:hover {
                border-color: #3b82f6;
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            }
            """
        ) as interface:

            # Enhanced Header
            gr.Markdown(
                """
                # 🚀 Professional Test Case Generator
                ### AI-Powered Vietnamese Test Case Generation with Advanced File Management

                **🎯 Key Features:** Project-Specific Storage • Professional File Gallery • Enhanced Upload System • Intelligent File Organization

                **📁 File Management:** .txt, .docx, .xlsx, .xls | **🤖 AI Models:** ChatGPT, Gemini | **🌐 Output:** Vietnamese Test Cases
                """,
                elem_classes=["main-header"]
            )

            # Divider
            gr.HTML('<div class="divider"></div>')
            
            with gr.Row(equal_height=True):
                with gr.Column(scale=1):
                    # Project Management Section
                    gr.Markdown("## 📁 Project Management", elem_classes=["section-header"])

                    with gr.Group(elem_classes=["professional-card"]):
                        gr.Markdown("### 🆕 Create New Project")
                        project_name_input = gr.Textbox(
                            label="Project Name",
                            placeholder="Enter project name...",
                            max_lines=1
                        )
                        project_desc_input = gr.Textbox(
                            label="Project Description",
                            placeholder="Brief description of the project scope...",
                            lines=2
                        )
                        create_project_btn = gr.Button(
                            "🆕 Create Project",
                            variant="primary",
                            size="lg",
                            elem_classes=["primary-btn"]
                        )

                    with gr.Group(elem_classes=["professional-card"]):
                        gr.Markdown("### 📂 Select Existing Project")
                        project_dropdown = gr.Dropdown(
                            label="Available Projects",
                            choices=self.get_project_choices(),
                            interactive=True
                        )
                        select_project_btn = gr.Button(
                            "📂 Select Project",
                            variant="secondary",
                            size="lg",
                            elem_classes=["secondary-btn"]
                        )

                    project_status = gr.Textbox(
                        label="Project Status",
                        interactive=False,
                        lines=3
                    )

                    # Enhanced File Management Section
                    with gr.Group(elem_classes=["file-management-card"]):
                        gr.Markdown("### 📁 Professional File Management")
                        gr.Markdown("*Advanced file organization with project-specific storage, visual indicators, and comprehensive management tools*")

                        file_management_dropdown = gr.Dropdown(
                            label="📂 Select Project for File Management",
                            choices=self.get_project_choices(),
                            interactive=True,
                            info="Choose a project to view and manage its files"
                        )

                        with gr.Row():
                            view_files_btn = gr.Button(
                                "📋 View Project Files",
                                variant="secondary",
                                size="lg",
                                elem_classes=["secondary-btn"]
                            )
                            refresh_files_btn = gr.Button(
                                "🔄 Refresh View",
                                variant="secondary",
                                elem_classes=["secondary-btn"]
                            )

                    # Enhanced file listing with professional display
                    file_listing = gr.Textbox(
                        label="📂 Professional File Structure & Management",
                        interactive=False,
                        lines=20,
                        visible=False,
                        show_copy_button=True,
                        info="Comprehensive view of all project files with management options"
                    )

                    # Enhanced file deletion section
                    with gr.Group(elem_classes=["professional-card"], visible=False) as file_actions_group:
                        gr.Markdown("### 🗑️ File Deletion Confirmation")
                        gr.Markdown("*Review file details before permanent deletion*")

                        with gr.Row():
                            file_id_input = gr.Textbox(
                                label="File ID",
                                visible=False
                            )
                            selected_project_input = gr.Textbox(
                                label="Selected Project",
                                visible=False
                            )

                        file_deletion_info = gr.Textbox(
                            label="📋 File Details & Deletion Warning",
                            interactive=False,
                            lines=12,
                            visible=False,
                            show_copy_button=False
                        )

                        with gr.Row(visible=False) as deletion_buttons:
                            confirm_delete_btn = gr.Button(
                                "🗑️ Confirm Deletion",
                                variant="stop",
                                size="lg",
                                elem_classes=["danger-btn"]
                            )
                            cancel_delete_btn = gr.Button(
                                "❌ Cancel Deletion",
                                variant="secondary",
                                size="lg",
                                elem_classes=["secondary-btn"]
                            )

                        deletion_result = gr.Textbox(
                            label="🔄 Deletion Status",
                            interactive=False,
                            lines=5,
                            visible=False,
                            show_copy_button=False
                        )

                with gr.Column(scale=2):
                    # Enhanced File Upload Section
                    gr.Markdown("## 📤 Professional Document Upload", elem_classes=["section-header"])

                    with gr.Group(elem_classes=["professional-card"]):
                        gr.Markdown("### 📋 Requirements & Specification Documents")
                        gr.Markdown("*Upload multiple files with automatic project-specific organization*")

                        file_upload = gr.Files(
                            label="📁 Select Requirement Files",
                            file_types=[".txt", ".docx", ".xlsx", ".xls"],
                            file_count="multiple",
                            elem_classes=["file-upload-area"]
                        )

                        gr.Markdown("""
                        **📋 Supported File Formats:**
                        - 📄 **Text Files** (.txt) - Plain text requirements and specifications
                        - 📝 **Word Documents** (.docx) - Formatted requirement documents
                        - 📊 **Excel Files** (.xlsx, .xls) - Structured requirements and data tables

                        **🎯 Features:**
                        - ✅ Project-specific storage in `projects/<project_name>/inputs/`
                        - ✅ Automatic file type detection and validation
                        - ✅ Original filename preservation
                        - ✅ File size and metadata tracking
                        """)

                        upload_btn = gr.Button(
                            "📤 Upload Documents to Project",
                            variant="primary",
                            size="lg",
                            elem_classes=["primary-btn"]
                        )

                    upload_status = gr.Textbox(
                        label="Upload Status",
                        interactive=False,
                        lines=4
                    )

            # Divider
            gr.HTML('<div class="divider"></div>')

            # Test Case Generation Section
            gr.Markdown("## 🤖 AI-Powered Test Case Generation", elem_classes=["section-header"])

            with gr.Row():
                with gr.Column(scale=1):
                    with gr.Group(elem_classes=["professional-card"]):
                        gr.Markdown("### ⚙️ Generation Settings")
                        ai_model_dropdown = gr.Dropdown(
                            label="AI Model Selection",
                            choices=self.test_generator.get_available_ai_models() + ["Mock (Built-in Generator)"],
                            value="Mock (Built-in Generator)" if not self.test_generator.get_available_ai_models() else self.test_generator.get_available_ai_models()[0],
                            interactive=True
                        )

                with gr.Column(scale=2):
                    with gr.Group(elem_classes=["professional-card"]):
                        gr.Markdown("### 🚀 Generate Professional Test Cases")
                        gr.Markdown("Click the button below to generate comprehensive Vietnamese test cases from your uploaded requirements.")
                        generate_btn = gr.Button(
                            "🚀 Generate Test Cases",
                            variant="primary",
                            size="lg",
                            elem_classes=["primary-btn"]
                        )

            # AI Service Status
            with gr.Group(elem_classes=["ai-status-card"]):
                ai_status = self.test_generator.get_ai_service_status()
                status_text = "🔧 **AI Services Status:**\n\n"

                if ai_status['openai']['available']:
                    status_text += f"✅ **ChatGPT**: Ready (Model: {ai_status['openai']['model']})\n"
                elif ai_status['openai']['configured']:
                    status_text += "⚠️ **ChatGPT**: Configured but unavailable\n"
                else:
                    status_text += "❌ **ChatGPT**: API key not configured\n"

                if ai_status['gemini']['available']:
                    status_text += f"✅ **Gemini**: Ready (Model: {ai_status['gemini']['model']})\n"
                elif ai_status['gemini']['configured']:
                    status_text += "⚠️ **Gemini**: Configured but unavailable\n"
                else:
                    status_text += "❌ **Gemini**: API key not configured\n"

                if not ai_status['openai']['available'] and not ai_status['gemini']['available']:
                    status_text += "\n💡 **Setup Guide:** Create a `.env` file and add your API keys to enable AI-powered generation"
                    status_text += "\n📖 See README.md for detailed configuration instructions"

                gr.Markdown(status_text)

            generation_status = gr.Textbox(
                label="Generation Status",
                interactive=False,
                lines=3
            )

            # Divider
            gr.HTML('<div class="divider"></div>')

            # Output Section
            gr.Markdown("## 📋 Generated Test Cases", elem_classes=["section-header"])

            with gr.Group(elem_classes=["professional-card"]):
                gr.Markdown("### 📝 Professional Vietnamese Test Cases")
                gr.Markdown("Generated test cases will appear below with proper structure: ID, Purpose, Steps, Expected Results, and Priority.")

                test_cases_output = gr.Textbox(
                    label="Generated Test Cases",
                    lines=25,
                    max_lines=35,
                    interactive=False,
                    show_copy_button=True
                )

            # Divider
            gr.HTML('<div class="divider"></div>')

            # Download Section
            gr.Markdown("## 💾 Export & Download", elem_classes=["section-header"])

            with gr.Group(elem_classes=["professional-card"]):
                gr.Markdown("### 📁 Download Generated Files")
                gr.Markdown("Export your test cases in multiple formats for easy integration with your testing workflow.")

                with gr.Row():
                    download_txt_btn = gr.DownloadButton(
                        "📄 Download TXT File",
                        variant="secondary",
                        size="lg",
                        elem_classes=["secondary-btn"]
                    )
                    download_excel_btn = gr.DownloadButton(
                        "📊 Download Excel File",
                        variant="secondary",
                        size="lg",
                        elem_classes=["secondary-btn"]
                    )

            download_info = gr.Textbox(
                label="File Information",
                interactive=False,
                lines=3
            )
            
            # Event handlers
            create_project_btn.click(
                fn=self.create_project,
                inputs=[project_name_input, project_desc_input],
                outputs=[project_status, project_dropdown]
            )
            
            select_project_btn.click(
                fn=self.select_project,
                inputs=[project_dropdown],
                outputs=[project_status]
            )
            
            upload_btn.click(
                fn=self.upload_files,
                inputs=[file_upload],
                outputs=[upload_status]
            )
            
            generate_btn.click(
                fn=self.generate_test_cases,
                inputs=[ai_model_dropdown],
                outputs=[generation_status, test_cases_output, download_info, download_txt_btn, download_excel_btn]
            )

            # File management event handlers
            def show_file_listing(project_name):
                if project_name:
                    file_data = self.get_file_management_data(project_name)
                    return gr.Textbox(value=file_data, visible=True)
                return gr.Textbox(visible=False)

            def handle_file_action(project_name, file_listing_content):
                """Handle file actions from the listing"""
                if not project_name or not file_listing_content:
                    return (
                        gr.Group(visible=False),  # file_actions_group
                        gr.Textbox(visible=False),  # file_deletion_info
                        gr.Row(visible=False),  # deletion_buttons
                        gr.Textbox(visible=False),  # deletion_result
                        "",  # file_id_input
                        ""   # selected_project_input
                    )

                # Check if there's a delete action in the content
                file_id, filename = self.parse_delete_action(file_listing_content)
                if file_id and filename:
                    # Show deletion confirmation
                    confirmation_text = self.get_file_details_for_confirmation(project_name, file_id)
                    return (
                        gr.Group(visible=True),   # file_actions_group
                        gr.Textbox(value=confirmation_text, visible=True),  # file_deletion_info
                        gr.Row(visible=True),     # deletion_buttons
                        gr.Textbox(visible=False), # deletion_result
                        file_id,                  # file_id_input
                        project_name              # selected_project_input
                    )

                return (
                    gr.Group(visible=False),  # file_actions_group
                    gr.Textbox(visible=False),  # file_deletion_info
                    gr.Row(visible=False),  # deletion_buttons
                    gr.Textbox(visible=False),  # deletion_result
                    "",  # file_id_input
                    ""   # selected_project_input
                )

            def confirm_file_deletion(project_name, file_id):
                """Confirm and execute file deletion"""
                result = self.delete_file(project_name, file_id)
                # Refresh file listing after deletion
                updated_listing = self.get_file_management_data(project_name)
                return (
                    gr.Textbox(value=result, visible=True),  # deletion_result
                    gr.Group(visible=False),  # file_actions_group
                    gr.Textbox(value=updated_listing, visible=True)  # file_listing
                )

            def cancel_file_deletion():
                """Cancel file deletion"""
                return (
                    gr.Group(visible=False),  # file_actions_group
                    gr.Textbox(visible=False),  # deletion_result
                )

            # Event bindings
            view_files_btn.click(
                fn=show_file_listing,
                inputs=[file_management_dropdown],
                outputs=[file_listing]
            )

            refresh_files_btn.click(
                fn=show_file_listing,
                inputs=[file_management_dropdown],
                outputs=[file_listing]
            )

            # Handle file actions (like delete clicks)
            file_listing.change(
                fn=handle_file_action,
                inputs=[file_management_dropdown, file_listing],
                outputs=[file_actions_group, file_deletion_info, deletion_buttons, deletion_result, file_id_input, selected_project_input]
            )

            # Confirm deletion
            confirm_delete_btn.click(
                fn=confirm_file_deletion,
                inputs=[selected_project_input, file_id_input],
                outputs=[deletion_result, file_actions_group, file_listing]
            )

            # Cancel deletion
            cancel_delete_btn.click(
                fn=cancel_file_deletion,
                outputs=[file_actions_group, deletion_result]
            )

            # Download buttons are handled automatically by Gradio when file paths are set
            
            # Divider
            gr.HTML('<div class="divider"></div>')

            # Footer
            gr.Markdown(
                """
                ---
                **Professional Test Case Generator v2.0** | Enterprise-Grade AI-Powered Testing Solution
                🚀 **Features:** Multi-AI Support • Vietnamese Output • Professional Formatting • Export Options
                💡 **Powered by:** OpenAI ChatGPT • Google Gemini • Gradio Framework
                """,
                elem_classes=["main-header"]
            )
        
        return interface


def main():
    """Main function to run the application"""
    app = TestCaseGeneratorApp()
    interface = app.create_interface()
    
    # Launch the application
    interface.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True,
        show_error=True,
        inbrowser=True
    )


if __name__ == "__main__":
    main()
