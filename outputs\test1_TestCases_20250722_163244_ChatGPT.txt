================================================================================
TEST CASES - TEST1
Generated on: 2025-07-22 16:32:44
================================================================================


============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_001
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_002
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_003
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_004
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_005
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_006
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_007
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_008
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_009
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_010
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_011
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_012
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_013
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_014
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_015
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_016
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_017
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_018
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_019
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_020
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_021
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_022
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_023
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_024
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_025
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_026
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_027
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_028
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_029
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_030
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_031
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE TÌM KIẾM
============================================================



============================================================
KIỂM TRA CHỨC NĂNG TÌM KIẾM
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_032
Mục đích kiểm thử: Kiểm tra tìm kiếm với từ khóa chính xác
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập từ khóa chính xác vào ô tìm kiếm
  2. Nhấn Enter hoặc nút Tìm kiếm

Kết quả mong muốn:
  Hiển thị danh sách kết quả khớp với từ khóa

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE TÌM KIẾM
============================================================



============================================================
KIỂM TRA CHỨC NĂNG TÌM KIẾM
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_033
Mục đích kiểm thử: Kiểm tra tìm kiếm với từ khóa chính xác
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập từ khóa chính xác vào ô tìm kiếm
  2. Nhấn Enter hoặc nút Tìm kiếm

Kết quả mong muốn:
  Hiển thị danh sách kết quả khớp với từ khóa

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_034
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_035
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_036
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_037
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_038
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_039
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_040
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_041
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_042
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE TÌM KIẾM
============================================================



============================================================
KIỂM TRA CHỨC NĂNG TÌM KIẾM
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_043
Mục đích kiểm thử: Kiểm tra tìm kiếm với từ khóa chính xác
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập từ khóa chính xác vào ô tìm kiếm
  2. Nhấn Enter hoặc nút Tìm kiếm

Kết quả mong muốn:
  Hiển thị danh sách kết quả khớp với từ khóa

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_044
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_045
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_046
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_047
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_048
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_049
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_050
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_051
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_052
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_053
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_054
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_055
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_056
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_057
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_058
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_059
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_060
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_061
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_062
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_063
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_064
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_065
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_066
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_067
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_068
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_069
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_070
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_071
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_072
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_073
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_074
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_075
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_076
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_077
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_078
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_079
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_080
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_081
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_082
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_083
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_084
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_085
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_086
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_087
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_088
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_089
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_090
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_091
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_092
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_093
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_094
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_095
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_096
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_097
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_098
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_099
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_100
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_101
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_102
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_103
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_104
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_105
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_106
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_107
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_108
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_109
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_110
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_111
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_112
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_113
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_114
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_115
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_116
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_117
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_118
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_119
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_120
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_121
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_122
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_123
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_124
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_125
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_126
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_127
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_128
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_129
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_130
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_131
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_132
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_133
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_134
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_135
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_136
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_137
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_138
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE TÌM KIẾM
============================================================



============================================================
KIỂM TRA CHỨC NĂNG TÌM KIẾM
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_139
Mục đích kiểm thử: Kiểm tra tìm kiếm với từ khóa chính xác
Độ ưu tiên: High

Các bước thực hiện:
  1. Nhập từ khóa chính xác vào ô tìm kiếm
  2. Nhấn Enter hoặc nút Tìm kiếm

Kết quả mong muốn:
  Hiển thị danh sách kết quả khớp với từ khóa

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_140
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_141
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_142
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_143
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_144
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_145
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_146
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_147
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_148
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_149
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_150
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_151
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_152
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_153
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_154
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_155
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_156
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_157
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_158
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_159
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_160
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_161
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_162
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_163
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_164
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_165
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_166
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_167
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_168
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_169
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_170
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_171
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_172
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_173
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_174
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_175
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_176
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_177
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_178
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_179
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_180
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_181
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_182
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_183
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_184
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_185
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_186
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_187
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_188
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_189
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_190
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_191
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_192
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_193
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_194
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_195
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_196
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_197
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_198
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_199
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_200
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_201
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_202
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_203
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_204
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_205
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_206
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_207
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_208
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_209
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_210
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_211
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_212
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_213
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_214
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_215
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_216
Mục đích kiểm thử: Kiểm tra hiển thị popup Sửa
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Sửa"

Kết quả mong muốn:
  Popup "Sửa" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE SỬA
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_217
Mục đích kiểm thử: Kiểm tra sửa với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_218
Mục đích kiểm thử: Kiểm tra sửa khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Sửa
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG SỬA
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_219
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_220
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_221
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================

Test Case #1
----------------------------------------
ID: TC_UI_222
Mục đích kiểm thử: Kiểm tra hiển thị popup Tạo mới
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở phân hệ quản trị
  2. Nhấn nút "Tạo mới"

Kết quả mong muốn:
  Popup "Tạo mới" hiển thị đúng với các trường và nút chức năng

--------------------------------------------------------------------------------



============================================================
KIỂM TRA VALIDATE TẠO MỚI
============================================================

Test Case #1
----------------------------------------
ID: TC_VAL_223
Mục đích kiểm thử: Kiểm tra tạo mới với thông tin hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Nhập thông tin hợp lệ
  3. Nhấn Lưu

Kết quả mong muốn:
  Thao tác thành công, hiển thị thông báo thành công

--------------------------------------------------------------------------------

Test Case #2
----------------------------------------
ID: TC_VAL_224
Mục đích kiểm thử: Kiểm tra tạo mới khi bỏ trống trường bắt buộc
Độ ưu tiên: High

Các bước thực hiện:
  1. Mở popup Tạo mới
  2. Để trống trường bắt buộc
  3. Nhấn Lưu

Kết quả mong muốn:
  Hiển thị thông báo lỗi yêu cầu nhập trường bắt buộc

--------------------------------------------------------------------------------



============================================================
KIỂM TRA CHỨC NĂNG TẠO MỚI
============================================================



============================================================
KIỂM TRA UI
============================================================



============================================================
KIỂM TRA VALIDATE XÓA
============================================================



============================================================
KIỂM TRA CHỨC NĂNG XÓA
============================================================

Test Case #1
----------------------------------------
ID: TC_FUN_225
Mục đích kiểm thử: Kiểm tra xóa bản ghi hợp lệ
Độ ưu tiên: High

Các bước thực hiện:
  1. Chọn bản ghi cần xóa
  2. Nhấn nút Xóa
  3. Xác nhận xóa

Kết quả mong muốn:
  Bản ghi được xóa thành công, hiển thị thông báo

--------------------------------------------------------------------------------


Total Test Cases Generated: 225
Generated by Test Case Generator v1.0
