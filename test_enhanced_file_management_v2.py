#!/usr/bin/env python3
"""
Enhanced File Management Test Suite
Tests the new file upload, visualization, and management features
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import TestCaseGeneratorApp
from project_manager import ProjectManager
from file_parser import FileParser


def create_test_files():
    """Create temporary test files for testing"""
    test_files = {}
    
    # Create a temporary directory
    temp_dir = tempfile.mkdtemp()
    
    # Create test .txt file
    txt_file = os.path.join(temp_dir, "test_requirements.txt")
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write("""
Chức năng cần kiểm thử (URD)
1.1. <PERSON><PERSON><PERSON> đích
Tài liệu này mô tả yêu cầu của người dùng đối với chức năng <PERSON>ản lý chức danh.

1.2. <PERSON><PERSON><PERSON> vi
Áp dụng cho phân hệ Quản trị hệ thống.

2.1. <PERSON><PERSON><PERSON> mới chức danh
Màn hình Popup "Tạo mới chức danh"
Trường nhập: Tên chức danh (bắt buộc, tối đa 64 ký tự, không trùng lặp)
Mô tả (không bắt buộc, tối đa 256 ký tự)
        """)
    
    # Create test .docx file (mock - we'll use the existing one)
    docx_file = "Quản lý mẫu đánh giá.docx"
    if os.path.exists(docx_file):
        test_files['docx'] = docx_file
    
    test_files['txt'] = txt_file
    test_files['temp_dir'] = temp_dir
    
    return test_files


def test_project_creation():
    """Test project creation with directory structure"""
    print("🧪 Testing Project Creation...")
    
    app = TestCaseGeneratorApp()
    
    # Create a test project
    result, dropdown = app.create_project("Enhanced_Test_Project", "Testing enhanced file management")
    print(f"Project Creation Result: {result}")
    
    # Verify project directories were created
    project_id = app.current_project_id
    if project_id:
        project_path = app.project_manager.get_project_path(project_id)
        inputs_path = app.project_manager.get_project_inputs_path(project_id)
        outputs_path = app.project_manager.get_project_outputs_path(project_id)
        
        print(f"✅ Project Path: {project_path}")
        print(f"✅ Inputs Path: {inputs_path}")
        print(f"✅ Outputs Path: {outputs_path}")
        
        # Check if directories exist
        if os.path.exists(inputs_path) and os.path.exists(outputs_path):
            print("✅ Project directories created successfully!")
        else:
            print("❌ Project directories not created properly!")
    
    return app


def test_file_upload(app):
    """Test enhanced file upload functionality"""
    print("\n🧪 Testing Enhanced File Upload...")
    
    test_files = create_test_files()
    
    # Mock file objects for testing
    class MockFile:
        def __init__(self, path, name):
            self.name = path
            self.original_name = name
    
    # Test uploading files
    files_to_upload = []
    if test_files.get('txt'):
        files_to_upload.append(MockFile(test_files['txt'], "test_requirements.txt"))
    
    if test_files.get('docx'):
        files_to_upload.append(MockFile(test_files['docx'], "test_document.docx"))
    
    if files_to_upload:
        result = app.upload_files(files_to_upload)
        print(f"Upload Result: {result}")
        
        # Verify files were uploaded to project-specific directory
        project_id = app.current_project_id
        inputs_path = app.project_manager.get_project_inputs_path(project_id)
        
        uploaded_files = os.listdir(inputs_path) if os.path.exists(inputs_path) else []
        print(f"✅ Files in project inputs directory: {uploaded_files}")
    
    # Cleanup
    if test_files.get('temp_dir'):
        shutil.rmtree(test_files['temp_dir'])
    
    return app


def test_file_visualization(app):
    """Test enhanced file visualization"""
    print("\n🧪 Testing Enhanced File Visualization...")
    
    project_name = "Enhanced_Test_Project"
    file_management_data = app.get_file_management_data(project_name)
    
    print("📂 File Management Display:")
    print("=" * 80)
    print(file_management_data)
    print("=" * 80)
    
    # Test file type icons
    test_extensions = ['.txt', '.docx', '.xlsx', '.xls', '.pdf']
    print("\n🎨 File Type Icons:")
    for ext in test_extensions:
        icon = app.get_file_type_icon(ext)
        description = app.get_file_type_description(ext)
        print(f"  {ext}: {icon} - {description}")


def test_file_deletion_confirmation(app):
    """Test enhanced file deletion confirmation"""
    print("\n🧪 Testing Enhanced File Deletion...")
    
    project_name = "Enhanced_Test_Project"
    project = app.project_manager.get_project_by_name(project_name)
    
    if project:
        files = app.project_manager.get_project_files(project['id'])
        if files:
            # Test deletion confirmation for the first file
            file_id = files[0].get('id')
            if file_id:
                confirmation_text = app.get_file_details_for_confirmation(project_name, file_id)
                print("🗑️ Deletion Confirmation Dialog:")
                print("=" * 80)
                print(confirmation_text)
                print("=" * 80)
            else:
                print("⚠️ No file ID found for deletion test")
        else:
            print("⚠️ No files found for deletion test")
    else:
        print("❌ Project not found for deletion test")


def test_project_statistics(app):
    """Test project statistics and file management features"""
    print("\n🧪 Testing Project Statistics...")
    
    project_name = "Enhanced_Test_Project"
    project = app.project_manager.get_project_by_name(project_name)
    
    if project:
        project_id = project['id']
        stats = app.project_manager.get_file_stats(project_id)
        categorized = app.project_manager.get_project_files_by_category(project_id)
        
        print("📊 Project Statistics:")
        print(f"  • Total Files: {stats['total_files']}")
        print(f"  • Input Files: {stats['input_files']}")
        print(f"  • Output Files: {stats['output_files']}")
        print(f"  • Total Size: {app.project_manager.format_file_size(stats['total_size'])}")
        
        print("\n📁 File Categories:")
        for category, files in categorized.items():
            print(f"  • {category.title()}: {len(files)} files")
            for file_info in files:
                print(f"    - {file_info.get('original_name', 'Unknown')}")


def run_comprehensive_test():
    """Run comprehensive test suite"""
    print("🚀 Starting Enhanced File Management Test Suite")
    print("=" * 80)
    
    try:
        # Test 1: Project Creation
        app = test_project_creation()
        
        # Test 2: File Upload
        app = test_file_upload(app)
        
        # Test 3: File Visualization
        test_file_visualization(app)
        
        # Test 4: File Deletion Confirmation
        test_file_deletion_confirmation(app)
        
        # Test 5: Project Statistics
        test_project_statistics(app)
        
        print("\n✅ All tests completed successfully!")
        print("🎉 Enhanced file management system is working properly!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_comprehensive_test()
